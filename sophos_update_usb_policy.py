# Demo code sample. Not intended for production use.
import requests

def execute():
    # Using the exact data region from the screenshot
    data_region = "us-west-2-fa88"  # From the API URL in the screenshot
    policy_id = "649e3fd7c86dd2335bdb875b"  # The policy ID from the screenshot
    
    request_url = f"https://dzr-api-amzn-{data_region}.api-upe.p.hmr.sophos.com/api/v1/policies/{policy_id}"
    
    # Update peripheral control settings
    request_body = {
        "settings": {
            "peripheralControl": {
                "enabled": True,
                "controlMode": "control"  # Change from "monitor" to "control"
                # Add other specific settings as needed
            }
        }
    }
    
    request_headers = {
        "Authorization": "Bearer 14be0917-5355-47ed-99a0-10062a3a66de",
        "Accept": "application/json",
        "Content-Type": "application/json"
    }

    response = requests.patch(request_url, headers=request_headers, json=request_body)
    
    if response.status_code == 200:
        print("USB Policy updated successfully:")
        print(response.json())
    else:
        print(f"Error: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    execute()